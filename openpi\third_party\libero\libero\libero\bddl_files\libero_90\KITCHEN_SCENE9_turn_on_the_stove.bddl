(define (problem LIBERO_Kitchen_Tabletop_Manipulation)
  (:domain robosuite)
  (:language turn on the stove)
    (:regions
      (flat_stove_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.21000000000000002 0.29 -0.19 0.31)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (wooden_two_layer_shelf_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.01 -0.26 0.01 -0.24)
            )
          )
          (:yaw_rotation (
              (3.141592653589793 3.141592653589793)
            )
          )
      )
      (frypan_init_region
          (:target kitchen_table)
          (:ranges (
              (0.025 -0.025 0.07500000000000001 0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (white_bowl_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.175 0.07500000000000001 -0.125 0.125)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (cook_region
          (:target flat_stove_1)
      )
      (top_side
          (:target wooden_two_layer_shelf_1)
      )
      (top_region
          (:target wooden_two_layer_shelf_1)
      )
      (bottom_region
          (:target wooden_two_layer_shelf_1)
      )
    )

  (:fixtures
    kitchen_table - kitchen_table
    flat_stove_1 - flat_stove
    wooden_two_layer_shelf_1 - wooden_two_layer_shelf
  )

  (:objects
    white_bowl_1 - white_bowl
    chefmate_8_frypan_1 - chefmate_8_frypan
  )

  (:obj_of_interest
    flat_stove_1
  )

  (:init
    (On flat_stove_1 kitchen_table_flat_stove_init_region)
    (On chefmate_8_frypan_1 kitchen_table_frypan_init_region)
    (On white_bowl_1 kitchen_table_white_bowl_init_region)
    (On wooden_two_layer_shelf_1 kitchen_table_wooden_two_layer_shelf_init_region)
  )

  (:goal
    (And (Turnon flat_stove_1))
  )

)
