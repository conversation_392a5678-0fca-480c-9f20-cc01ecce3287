(define (problem LIBERO_Tabletop_Manipulation)
  (:domain robosuite)
  (:language Pick the akita black bowl on the stove and place it on the plate)
    (:regions
      (plate_region
          (:target main_table)
          (:ranges (
              (0.05 0.19 0.07 0.21)
            )
          )
      )
      (next_to_plate_region
          (:target main_table)
          (:ranges (
              (0.0 0.3 0.02 0.32)
            )
          )
      )
      (box_region
          (:target main_table)
          (:ranges (
              (0.06 0.02 0.08 0.04)
            )
          )
      )
      (next_to_box_region
          (:target main_table)
          (:ranges (
              (0.12 -0.08 0.14 -0.06)
            )
          )
      )
      (between_plate_ramekin_region
          (:target main_table)
          (:ranges (
              (-0.06 0.19 -0.04 0.21)
            )
          )
      )
      (ramekin_region
          (:target main_table)
          (:ranges (
              (-0.21 0.19 -0.19 0.21)
            )
          )
      )
      (next_to_ramekin_region
          (:target main_table)
          (:ranges (
              (-0.19 0.31 -0.17 0.33)
            )
          )
      )
      (table_center
          (:target main_table)
          (:ranges (
              (-0.1 -0.01 -0.05 0.01)
            )
          )
      )
      (table_front
          (:target main_table)
          (:ranges (
              (0.19 -0.01 0.21 0.01)
            )
          )
      )
      (cabinet_region
          (:target main_table)
          (:ranges (
              (0.02 -0.28 0.04 -0.26)
            )
          )
          (:yaw_rotation (
              (2.6613777765410678 2.7242096296128633)
            )
          )
      )
      (stove_region
          (:target main_table)
          (:ranges (
              (-0.42 -0.15 -0.4 -0.13)
            )
          )
      )
      (top_region
          (:target wooden_cabinet_1)
      )
      (middle_region
          (:target wooden_cabinet_1)
      )
      (bottom_region
          (:target wooden_cabinet_1)
      )
      (top_side
          (:target wooden_cabinet_1)
      )
      (cook_region
          (:target flat_stove_1)
      )
    )

  (:fixtures
    main_table - table
    wooden_cabinet_1 - wooden_cabinet
    flat_stove_1 - flat_stove
  )

  (:objects
    akita_black_bowl_1 akita_black_bowl_2 - akita_black_bowl
    cookies_1 - cookies
    glazed_rim_porcelain_ramekin_1 - glazed_rim_porcelain_ramekin
    plate_1 - plate
  )

  (:obj_of_interest
    akita_black_bowl_1
    plate_1
  )

  (:init
    (On akita_black_bowl_1 flat_stove_1_cook_region)
    (On akita_black_bowl_2 wooden_cabinet_1_top_side)
    (On plate_1 main_table_plate_region)
    (On cookies_1 main_table_box_region)
    (On glazed_rim_porcelain_ramekin_1 main_table_ramekin_region)
    (On wooden_cabinet_1 main_table_cabinet_region)
    (On flat_stove_1 main_table_stove_region)
  )

  (:goal
    (And (On akita_black_bowl_1 plate_1))
  )

)
