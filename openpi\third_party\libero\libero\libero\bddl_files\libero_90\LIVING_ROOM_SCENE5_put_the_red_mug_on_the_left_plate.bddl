(define (problem LIBERO_Living_Room_Tabletop_Manipulation)
  (:domain robosuite)
  (:language put the red mug on the left plate)
    (:regions
      (plate_left_region
          (:target living_room_table)
          (:ranges (
              (-0.025 -0.325 0.025 -0.27499999999999997)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (plate_right_region
          (:target living_room_table)
          (:ranges (
              (-0.025 0.27499999999999997 0.025 0.325)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (porcelain_mug_init_region
          (:target living_room_table)
          (:ranges (
              (-0.125 -0.175 -0.07500000000000001 -0.125)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (white_yellow_mug_init_region
          (:target living_room_table)
          (:ranges (
              (-0.07500000000000001 0.07500000000000001 -0.025 0.125)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (red_coffee_mug_init_region
          (:target living_room_table)
          (:ranges (
              (-0.225 -0.025 -0.17500000000000002 0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
    )

  (:fixtures
    living_room_table - living_room_table
  )

  (:objects
    porcelain_mug_1 - porcelain_mug
    red_coffee_mug_1 - red_coffee_mug
    white_yellow_mug_1 - white_yellow_mug
    plate_1 plate_2 - plate
  )

  (:obj_of_interest
    red_coffee_mug_1
    plate_1
  )

  (:init
    (On plate_1 living_room_table_plate_left_region)
    (On plate_2 living_room_table_plate_right_region)
    (On red_coffee_mug_1 living_room_table_red_coffee_mug_init_region)
    (On white_yellow_mug_1 living_room_table_white_yellow_mug_init_region)
    (On porcelain_mug_1 living_room_table_porcelain_mug_init_region)
  )

  (:goal
    (And (On red_coffee_mug_1 plate_1))
  )

)
