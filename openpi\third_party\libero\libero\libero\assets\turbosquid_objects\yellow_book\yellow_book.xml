<mujoco model="yellow_book">
  <asset>
  <texture file="yellow_book.png" name="tex-yellow_book" type="2d" />
  <material name="yellow_book" reflectance="0.5" texrepeat="1 1" texture="tex-yellow_book" texuniform="false" />
  <mesh file="visual/yellow_book_vis.msh" name="yellow_book_vis" scale="0.4 0.4 0.4" /></asset>
  <worldbody>
    <body>
      <body name="object">
      <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="yellow_book_vis" conaffinity="0" contype="0" group="1" material="yellow_book" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.00164 0.00008 0.06309" quat="0.00000 0.00000 1.00000 0.00000" size="0.01177 0.04887 0.06112" group="0" rgba="0.8 0.8 0.8 0.3" /></body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.04" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.025 0.025 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>