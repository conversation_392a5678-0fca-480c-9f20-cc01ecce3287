(define (problem LIBERO_Kitchen_Tabletop_Manipulation)
  (:domain robosuite)
  (:language close the microwave)
    (:regions
      (microwave_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.01 0.33999999999999997 0.01 0.36)
            )
          )
          (:yaw_rotation (
              (0 0)
            )
          )
      )
      (white_yellow_mug_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.025 -0.025 0.025 0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (porcelain_mug_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.125 -0.275 -0.07500000000000001 -0.225)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (porcelain_mug_front_region
          (:target kitchen_table)
          (:ranges (
              (-0.05 -0.3 0.05 -0.2)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (top_side
          (:target microwave_1)
      )
      (heating_region
          (:target microwave_1)
      )
    )

  (:fixtures
    kitchen_table - kitchen_table
    microwave_1 - microwave
  )

  (:objects
    porcelain_mug_1 - porcelain_mug
    white_yellow_mug_1 - white_yellow_mug
  )

  (:obj_of_interest
    microwave_1
  )

  (:init
    (On porcelain_mug_1 kitchen_table_porcelain_mug_init_region)
    (On white_yellow_mug_1 kitchen_table_white_yellow_mug_init_region)
    (On microwave_1 kitchen_table_microwave_init_region)
    (Open microwave_1)
  )

  (:goal
    (And (Close microwave_1))
  )

)
