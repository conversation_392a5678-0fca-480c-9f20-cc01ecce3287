{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mNo private macro file found! (__init__.py:7)\n", "\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mIt is recommended to use a private macro file (__init__.py:8)\n", "\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mTo setup, run: python /home/<USER>/workspace/robosuite-master/robosuite/scripts/setup_macros.py (__init__.py:9)\n"]}], "source": ["import os\n", "import re\n", "import numpy as np\n", "\n", "from robosuite.models.objects import MujocoXMLObject\n", "from robosuite.utils.mjcf_utils import xml_path_completion\n", "\n", "from libero.libero.envs.base_object import register_object\n", "\n", "import pathlib\n", "\n", "from libero.libero.envs.base_object import (\n", "    register_visual_change_object,\n", "    register_object,\n", ")\n", "from libero.libero.utils.mu_utils import register_mu, InitialSceneTemplates\n", "from libero.libero.utils.task_generation_utils import register_task_info, get_task_info, generate_bddl_from_task_info\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Define your own objects\n", "You may want to include more object meshes of yours in the procedural generation pipeline. One option is to include your assets and define your object directly inside the LIBERO codebase. But this can make the whole thing messy. \n", "\n", "Alternatively, you can define the objects inside your custom project repo folder, and define the object classes accordingly. Note that you need to import your defined object classes whenever you run your own stuff. Libero codebase cannot automatically import those that are defined outside its repo.\n", "\n", "In the next, we provide an example, assuming you have object meses defined in `custom_assets`. In this example, we assume the generated pddl file will be saved in `custom_pddl`."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "class CustomObjects(MujocoXMLObject):\n", "    def __init__(self, custom_path, name, obj_name, joints=[dict(type=\"free\", damping=\"0.0005\")]):\n", "        # make sure custom path is an absolute path\n", "        assert(os.path.isabs(custom_path)), \"Custom path must be an absolute path\"\n", "        # make sure the custom path is also an xml file\n", "        assert(custom_path.endswith(\".xml\")), \"Custom path must be an xml file\"\n", "        super().__init__(\n", "            custom_path,\n", "            name=name,\n", "            joints=joints,\n", "            obj_type=\"all\",\n", "            duplicate_collision_geoms=False,\n", "        )\n", "        self.category_name = \"_\".join(\n", "            re.sub(r\"([A-Z])\", r\" \\1\", self.__class__.__name__).split()\n", "        ).lower()\n", "        self.object_properties = {\"vis_site_names\": {}}\n", "\n", "@register_object\n", "class LiberoMug(CustomObjects):\n", "    def __init__(self,\n", "                 name=\"libero_mug\",\n", "                 obj_name=\"libero_mug\",\n", "                 ):\n", "        super().__init__(\n", "            custom_path=os.path.abspath(os.path.join(\n", "                \"./\", \"custom_assets\", \"libero_mug\", \"libero_mug.xml\"\n", "            )),\n", "            name=name,\n", "            obj_name=obj_name,\n", "        )\n", "\n", "        self.rotation = {\n", "            \"x\": (-np.pi/2, -np.pi/2),\n", "            \"y\": (-np.pi, -np.pi),\n", "            \"z\": (np.pi, np.pi),\n", "        }\n", "        self.rotation_axis = None\n", "\n", "@register_object\n", "class LiberoMugYellow(CustomObjects):\n", "    def __init__(self,\n", "                 name=\"libero_mug\",\n", "                 obj_name=\"libero_mug\",\n", "                 ):\n", "        super().__init__(\n", "            custom_path=os.path.abspath(os.path.join(\n", "                \"./\", \"custom_assets\", \"libero_mug_yellow\", \"libero_mug_yellow.xml\"\n", "            )),\n", "            name=name,\n", "            obj_name=obj_name,\n", "        )\n", "\n", "        self.rotation = {\n", "            \"x\": (-np.pi/2, -np.pi/2),\n", "            \"y\": (-np.pi, -np.pi),\n", "            \"z\": (np.pi, np.pi),\n", "        }\n", "        self.rotation_axis = None\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Define the scene\n", "Now we define the scene to load the previously defined objects. For more information about the scene genration, please look at `procedural_creation_walkthrough.ipynb`. "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import re\n", "from libero.libero.envs import objects\n", "from libero.libero.utils.bddl_generation_utils import *\n", "from libero.libero.envs.objects import OBJECTS_DICT\n", "from libero.libero.utils.object_utils import get_affordance_regions\n", "\n", "from libero.libero.utils.mu_utils import register_mu, InitialSceneTemplates\n", "\n", "@register_mu(scene_type=\"kitchen\")\n", "class KitchenDemoScene(InitialSceneTemplates):\n", "    def __init__(self):\n", "\n", "        fixture_num_info = {\n", "            \"kitchen_table\": 1,\n", "            \"wooden_cabinet\": 1,\n", "        }\n", "\n", "        object_num_info = {\n", "            \"libero_mug\": 1,\n", "            \"libero_mug_yellow\": 1,\n", "        }\n", "\n", "        super().__init__(\n", "            workspace_name=\"kitchen_table\",\n", "            fixture_num_info=fixture_num_info,\n", "            object_num_info=object_num_info,\n", "        )\n", "\n", "    def define_regions(self):\n", "        self.regions.update(\n", "            self.get_region_dict(\n", "                region_centroid_xy=[0.0, -0.30],\n", "                region_name=\"wooden_cabinet_init_region\",\n", "                target_name=self.workspace_name,\n", "                region_half_len=0.01,\n", "                yaw_rotation=(np.pi, np.pi),\n", "            )\n", "        )\n", "\n", "        self.regions.update(\n", "            self.get_region_dict(\n", "                region_centroid_xy=[0.0, 0.0],\n", "                region_name=\"libero_mug_init_region\",\n", "                target_name=self.workspace_name,\n", "                region_half_len=0.025,\n", "            )\n", "        )\n", "\n", "        self.regions.update(\n", "            self.get_region_dict(\n", "                region_centroid_xy=[-0.1, 0.15],\n", "                region_name=\"libero_mug_yellow_init_region\",\n", "                target_name=self.workspace_name,\n", "                region_half_len=0.025,\n", "            )\n", "        )\n", "        self.xy_region_kwargs_list = get_xy_region_kwargs_list_from_regions_info(\n", "            self.regions\n", "        )\n", "\n", "    @property\n", "    def init_states(self):\n", "        states = [\n", "            (\"On\", \"libero_mug_1\", \"kitchen_table_libero_mug_init_region\"),\n", "            (\"On\", \"libero_mug_yellow_1\", \"kitchen_table_libero_mug_yellow_init_region\"),\n", "            (\"On\", \"wooden_cabinet_1\", \"kitchen_table_wooden_cabinet_init_region\"),\n", "        ]\n", "        return states"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Succefully generated: 1\n", "['./custom_pddl/KITCHEN_DEMO_SCENE_libero_demo_behaviors.bddl']\n", "Encountered some failures:  []\n", "(define (problem LIBERO_Kitchen_Tabletop_Manipulation)\n", "  (:domain robosuite)\n", "  (:language libero demo behaviors)\n", "    (:regions\n", "      (wooden_cabinet_init_region\n", "          (:target kitchen_table)\n", "          (:ranges (\n", "              (-0.01 -0.31 0.01 -0.29)\n", "            )\n", "          )\n", "          (:yaw_rotation (\n", "              (3.141592653589793 3.141592653589793)\n", "            )\n", "          )\n", "      )\n", "      (libero_mug_init_region\n", "          (:target kitchen_table)\n", "          (:ranges (\n", "              (-0.025 -0.025 0.025 0.025)\n", "            )\n", "          )\n", "          (:yaw_rotation (\n", "              (0.0 0.0)\n", "            )\n", "          )\n", "      )\n", "      (libero_mug_yellow_init_region\n", "          (:target kitchen_table)\n", "          (:ranges (\n", "              (-0.125 0.125 -0.07500000000000001 0.175)\n", "            )\n", "          )\n", "          (:yaw_rotation (\n", "              (0.0 0.0)\n", "            )\n", "          )\n", "      )\n", "      (top_side\n", "          (:target wooden_cabinet_1)\n", "      )\n", "      (top_region\n", "          (:target wooden_cabinet_1)\n", "      )\n", "      (middle_region\n", "          (:target wooden_cabinet_1)\n", "      )\n", "      (bottom_region\n", "          (:target wooden_cabinet_1)\n", "      )\n", "    )\n", "\n", "  (:fixtures\n", "    kitchen_table - kitchen_table\n", "    wooden_cabinet_1 - wooden_cabinet\n", "  )\n", "\n", "  (:objects\n", "    libero_mug_1 - libero_mug\n", "    libero_mug_yellow_1 - libero_mug_yellow\n", "  )\n", "\n", "  (:obj_of_interest\n", "  )\n", "\n", "  (:init\n", "    (On libero_mug_1 kitchen_table_libero_mug_init_region)\n", "    (On libero_mug_yellow_1 kitchen_table_libero_mug_yellow_init_region)\n", "    (On wooden_cabinet_1 kitchen_table_wooden_cabinet_init_region)\n", "  )\n", "\n", "  (:goal\n", "    (And (Open wooden_cabinet_1_top_region) (In libero_mug_yellow_1 wooden_cabinet_1_top_region))\n", "  )\n", "\n", ")\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<PIL.Image.Image image mode=RGB size=256x256>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["scene_name = \"kitchen_demo_scene\"\n", "language = \"libero demo behaviors\"\n", "register_task_info(language,\n", "                   scene_name=scene_name,\n", "                   objects_of_interest=[],\n", "                   goal_states=[\n", "                       (\"Open\", \"wooden_cabinet_1_top_region\"),\n", "                       (\"In\", \"libero_mug_yellow_1\", \"wooden_cabinet_1_top_region\"),\n", "                       ],\n", ")\n", "\n", "YOUR_BDDL_FILE_PATH = \"./custom_pddl\"\n", "bddl_file_names, failures = generate_bddl_from_task_info(folder=YOUR_BDDL_FILE_PATH)\n", "print(bddl_file_names)\n", "\n", "print(\"Encountered some failures: \", failures)\n", "\n", "with open(bddl_file_names[0], \"r\") as f:\n", "    print(f.read())\n", "\n", "from libero.libero.envs import OffScreenRenderEnv\n", "from IPython.display import display\n", "from PIL import Image\n", "\n", "import torch\n", "import torchvision\n", "\n", "\n", "env_args = {\n", "    \"bddl_file_name\": bddl_file_names[0],\n", "    \"camera_heights\": 256,\n", "    \"camera_widths\": 256\n", "}\n", "\n", "env = OffScreenRenderEnv(**env_args)\n", "obs = env.reset()\n", "display(Image.fromarray(obs[\"agentview_image\"][::-1]))"]}], "metadata": {"kernelspec": {"display_name": "new-continual-learning", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}